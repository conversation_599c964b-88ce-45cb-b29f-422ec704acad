"""
Agent API路由

提供Agent相关的REST API接口，包括执行、列表查询和信息获取。
"""

from fastapi import APIRouter, HTTPException, Depends
from pydantic import BaseModel, Field
from typing import Dict, Any, Optional, List
import logging

from services.agent_service import AgentService
from infrastructure.dependencies import get_agent_service
from infrastructure.exceptions import ServiceError, AgentError

try:
    from autogen_core import TRACE_LOGGER_NAME
    AUTOGEN_AVAILABLE = True
except ImportError:
    TRACE_LOGGER_NAME = "autogen_core.trace"
    AUTOGEN_AVAILABLE = False

# 使用AutoGen的trace logger
logger = logging.getLogger(f"{TRACE_LOGGER_NAME}.agent_router")

# 创建路由器
router = APIRouter(prefix="/autogen", tags=["agents"])


# 请求和响应模型
class AgentRunRequest(BaseModel):
    """Agent执行请求模型"""
    name: str = Field(..., description="Agent名称/类型")
    message: str = Field(..., description="用户消息")
    model: Optional[str] = Field(None, description="模型别名, 替换Agent默认模型")
    options: Optional[Dict[str, Any]] = Field(None, description="其他执行选项")

    class Config:
        schema_extra = {
            "example": {
                "name": "code_reviewer",
                "message": "请审查这段Python代码：def hello(): print('Hello World')",
                "model": "Qwen2.5-32B-Instruct",
                "options": {
                    "timeout": 30,
                    "stream": False
                }
            },
            "examples": {
                "with_model": {
                    "summary": "使用指定模型的Agent（AssistantAgent）",
                    "value": {
                        "name": "code_reviewer",
                        "message": "请审查这段Python代码：def hello(): print('Hello World')",
                        "model": "Qwen2.5-32B-Instruct",
                        "options": {"timeout": 30}
                    }
                },
                "without_model": {
                    "summary": "不需要模型的Agent（UserProxyAgent）",
                    "value": {
                        "name": "user_proxy",
                        "message": "用户输入消息",
                        "options": {"stream": False}
                    }
                }
            }
        }


class AgentRunResponse(BaseModel):
    """Agent执行响应模型"""
    status: str = Field(..., description="执行状态")
    result: Any = Field(..., description="执行结果")
    metadata: Dict[str, Any] = Field(..., description="执行元数据")
    
    class Config:
        schema_extra = {
            "example": {
                "status": "success",
                "result": "代码审查结果：该函数实现正确，建议添加文档字符串。",
                "metadata": {
                    "agent_name": "code_reviewer",
                    "model": "Qwen2.5-32B-Instruct",
                    "execution_time": 2.5,
                    "message_length": 45
                }
            }
        }


class AgentListResponse(BaseModel):
    """Agent列表响应模型"""
    agents: List[str] = Field(..., description="可用Agent列表")
    total: int = Field(..., description="Agent总数")
    metadata: Dict[str, Any] = Field(..., description="列表元数据")


class AgentInfoResponse(BaseModel):
    """Agent信息响应模型"""
    name: str = Field(..., description="Agent名称")
    description: str = Field(..., description="Agent描述")
    capabilities: List[str] = Field(..., description="Agent能力列表")
    metadata: Dict[str, Any] = Field(..., description="Agent元数据")


# API端点
@router.post("/run/agent", response_model=AgentRunResponse)
async def run_agent(
    request: AgentRunRequest,
    service: AgentService = Depends(get_agent_service)
):
    """
    执行Agent任务
    
    执行指定的Agent，处理用户消息并返回结果。
    支持多种模型和执行选项。
    """
    try:
        model_info = f"with custom model: {request.model}" if request.model else "without custom model"
        logger.info(f"Running agent: {request.name} {model_info}")

        result = await service.run(
            name=request.name,
            message=request.message,
            model=request.model,
            **(request.options or {})
        )
        
        logger.info(f"Agent run completed: {request.name}")
        return AgentRunResponse(**result)
    
    except (ServiceError, AgentError) as e:
        logger.error(f"Agent service error: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    
    except Exception as e:
        logger.error(f"Unexpected error in agent run: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.get("/agents", response_model=AgentListResponse)
async def list_agents(
    service: AgentService = Depends(get_agent_service)
):
    """
    列出所有可用的Agent
    
    返回系统中注册的所有Agent类型及其基本信息。
    """
    try:
        logger.info("Listing available agents")
        
        result = await service.list_agents()
        
        # 提取agent名称列表
        agent_names = [agent["name"] for agent in result["agents"]]

        response = AgentListResponse(
            agents=agent_names,
            total=result["count"],
            metadata={"timestamp": result["timestamp"], "agents_detail": result["agents"]}
        )
        
        logger.info(f"Listed {result['count']} agents")
        return response
    
    except ServiceError as e:
        logger.error(f"Agent service error: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    
    except Exception as e:
        logger.error(f"Unexpected error in list agents: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")


@router.get("/agents/{name}", response_model=AgentInfoResponse)
async def get_agent_info(
    name: str,
    service: AgentService = Depends(get_agent_service)
):
    """
    获取指定Agent的详细信息
    
    返回Agent的描述、能力和配置信息。
    """
    try:
        logger.info(f"Getting agent info: {name}")
        
        result = await service.get_agent_info(name)
        
        agent_info = result["agent_info"]
        response = AgentInfoResponse(
            name=agent_info.get("name", name),
            description=agent_info.get("description", ""),
            capabilities=agent_info.get("capabilities", []),
            metadata={"timestamp": result["timestamp"]}
        )
        
        logger.info(f"Retrieved info for agent: {name}")
        return response
    
    except ServiceError as e:
        logger.error(f"Agent service error: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))
    
    except Exception as e:
        logger.error(f"Unexpected error in get agent info: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")
