#!/usr/bin/env python3
"""
测试Team中model参数可选功能

验证不同类型的Team对model参数的要求：
- RoundRobinGroupChat: 不需要model参数
- SelectorGroupChat: 需要model参数
- Swarm: 不需要model参数
- MagenticOneGroupChat: 需要model参数
"""

import sys
import os
sys.path.append(os.path.dirname(__file__))

def test_team_service_optional_model():
    """测试TeamService中model参数可选功能"""
    print("\n=== 测试TeamService model参数可选功能 ===")

    try:
        from services.team_service import TeamService

        service = TeamService()
        print("✓ TeamService初始化成功")

        # 测试参数验证（使用真实的team名称）
        available_teams = service.team_manager.list_available_teams()
        if available_teams:
            test_team = available_teams[0]

            # 测试model为None的情况
            service._validate_run_params(test_team, "test message", None)
            print("✓ model=None参数验证通过")

            # 测试model为空字符串的情况
            try:
                service._validate_run_params(test_team, "test message", "")
                print("✗ model=''应该验证失败")
            except Exception as e:
                print(f"✓ model=''验证失败（预期）: 参数验证正确")

            # 测试model为有效字符串的情况
            service._validate_run_params(test_team, "test message", "gpt-4")
            print("✓ model='gpt-4'参数验证通过")
        else:
            print("⚠ 没有可用的team，跳过参数验证测试")

        return True

    except Exception as e:
        print(f"✗ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_team_manager_optional_model():
    """测试TeamManager中model参数可选功能"""
    print("\n=== 测试TeamManager model参数可选功能 ===")
    
    try:
        from managers.team_manager import get_team_manager
        
        team_manager = get_team_manager()
        print("✓ TeamManager初始化成功")
        
        # 测试不需要model的Team类型
        try:
            team = team_manager.create_autogen_team(
                team_class='RoundRobinGroupChat',
                model_alias=None
            )
            print(f"✓ RoundRobinGroupChat创建成功（无model）: {team}")
        except Exception as e:
            print(f"⚠ RoundRobinGroupChat创建失败（预期，因为AutoGen可能不可用）: {e}")
        
        # 测试需要model的Team类型
        try:
            team = team_manager.create_autogen_team(
                team_class='SelectorGroupChat',
                model_alias=None
            )
            print("✗ SelectorGroupChat应该要求model参数")
        except ValueError as e:
            print(f"✓ SelectorGroupChat正确要求model参数: {e}")
        except Exception as e:
            print(f"⚠ SelectorGroupChat测试失败（其他原因）: {e}")
        
        # 测试SelectorGroupChat提供model的情况
        try:
            team = team_manager.create_autogen_team(
                team_class='SelectorGroupChat',
                model_alias='nebulacoder-v6.0'
            )
            print(f"✓ SelectorGroupChat创建成功（有model）: {team}")
        except Exception as e:
            print(f"⚠ SelectorGroupChat创建失败（预期，因为AutoGen可能不可用）: {e}")
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def test_team_router_request_model():
    """测试TeamRouter请求模型"""
    print("\n=== 测试TeamRouter请求模型 ===")
    
    try:
        from routers.team_router import TeamRunRequest
        
        # 测试不提供model的请求
        request1 = TeamRunRequest(
            name="development",
            message="test message"
        )
        print(f"✓ 无model请求创建成功: model={request1.model}")
        
        # 测试提供model的请求
        request2 = TeamRunRequest(
            name="advanced_development",
            message="test message",
            model="gpt-4"
        )
        print(f"✓ 有model请求创建成功: model={request2.model}")
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """运行所有测试"""
    print("开始测试Team model参数可选功能...")
    
    results = []
    
    # 运行各项测试
    results.append(test_team_service_optional_model())
    results.append(test_team_manager_optional_model())
    results.append(test_team_router_request_model())
    
    # 汇总结果
    passed = sum(results)
    total = len(results)
    
    print(f"\n=== 测试结果汇总 ===")
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有测试通过！Team model参数可选功能实现正确。")
        return True
    else:
        print("❌ 部分测试失败，需要检查实现。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
