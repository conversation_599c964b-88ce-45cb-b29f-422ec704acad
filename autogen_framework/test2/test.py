import os
os.environ['https_proxy'] = ''
os.environ['http_proxy'] = ''
os.environ['all_proxy'] = ''
os.environ['ftp_proxy'] = ''
os.environ['no_proxy'] = ''

from managers.agent_manager import get_agent_manager
agent_manager = get_agent_manager()
try:
    # 使用nebulacoder-v6.0模型
    agent = agent_manager.create_agent('developer', 'nebulacoder-v6.0', name='TestDeveloper')
    print('Success:', type(agent))
    print('Agent name:', getattr(agent, 'name', 'No name attribute'))
except Exception as e:
    print('Error:', e)