#!/usr/bin/env python3
"""
简单的Agent管理器功能测试
"""

import sys
import os
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# 模拟autogen和autogen_core模块
class MockConversableAgent:
    def __init__(self, name, system_message, llm_config, **kwargs):
        self.name = name
        self.system_message = system_message
        self.llm_config = llm_config
        self.kwargs = kwargs
    
    def __repr__(self):
        return f"MockConversableAgent(name='{self.name}')"

class MockModule:
    def __init__(self):
        self.ConversableAgent = MockConversableAgent
        self.TRACE_LOGGER_NAME = "autogen_core.trace"

# 设置模拟模块
sys.modules['autogen'] = MockModule()
sys.modules['autogen_core'] = MockModule()

# 模拟模型管理器
class MockModelManager:
    def get_model_config(self, model_alias):
        return {"model": model_alias, "temperature": 0.7}

# 模拟提示词管理器
class MockPromptManager:
    def get_prompt(self, prompt_path, **kwargs):
        return f"System prompt for {prompt_path}"

def mock_get_prompt_manager():
    return MockPromptManager()

# 设置模拟
sys.modules['managers.model_manager'] = type('MockModule', (), {'ModelManager': MockModelManager})()
sys.modules['managers.prompt_manager'] = type('MockModule', (), {'get_prompt_manager': mock_get_prompt_manager})()

def test_agent_manager():
    """测试Agent管理器基本功能"""
    print("=== 测试Agent管理器 ===")
    
    try:
        from managers.agent_manager import AgentManager
        
        # 创建临时目录
        import tempfile
        temp_dir = tempfile.mkdtemp()
        print(f"使用临时目录: {temp_dir}")
        
        # 创建Agent管理器
        model_manager = MockModelManager()
        agent_manager = AgentManager(temp_dir, model_manager)
        print("✓ Agent管理器创建成功")
        
        # 测试注册工厂函数
        def test_factory(model_alias, **kwargs):
            return MockConversableAgent(
                name=kwargs.get('name', 'TestAgent'),
                system_message="Test system message",
                llm_config={"model": model_alias}
            )
        
        agent_manager.register_agent_factory("test_agent", test_factory)
        print("✓ Agent工厂函数注册成功")
        
        # 测试列出可用Agent
        available = agent_manager.list_available_agents()
        print(f"✓ 可用Agent: {available}")
        
        # 测试创建Agent
        agent = agent_manager.create_agent("test_agent", "test-model")
        print(f"✓ Agent创建成功: {agent}")
        
        # 测试获取Agent信息
        info = agent_manager.get_agent_info("test_agent")
        print(f"✓ Agent信息: {info['name']}")
        
        # 清理
        import shutil
        shutil.rmtree(temp_dir)
        print("✓ 清理完成")
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_agent_creation():
    """测试Agent定义文件"""
    print("\n=== 测试Agent定义文件 ===")
    
    try:
        # 创建测试Agent文件
        agent_code = '''
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(__file__)))

class MockConversableAgent:
    def __init__(self, name, system_message, llm_config, **kwargs):
        self.name = name
        self.system_message = system_message
        self.llm_config = llm_config

class MockModelManager:
    def get_model_config(self, model_alias):
        return {"model": model_alias}

def get_prompt_manager():
    class MockPromptManager:
        def get_prompt(self, path, **kwargs):
            return "Mock prompt"
    return MockPromptManager()

def create_test_agent(model_alias, name=None, **kwargs):
    """创建测试Agent"""
    model_manager = MockModelManager()
    prompt_manager = get_prompt_manager()
    
    system_prompt = prompt_manager.get_prompt("agents/test", **kwargs)
    model_config = model_manager.get_model_config(model_alias)
    
    agent = MockConversableAgent(
        name=name or "TestAgent",
        system_message=system_prompt,
        llm_config=model_config,
        **kwargs
    )
    
    return agent
'''
        
        # 写入临时文件
        import tempfile
        with tempfile.NamedTemporaryFile(mode='w', suffix='_agent.py', delete=False) as f:
            f.write(agent_code)
            temp_file = f.name
        
        # 动态导入并测试
        import importlib.util
        spec = importlib.util.spec_from_file_location("test_agent", temp_file)
        module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(module)
        
        # 测试工厂函数
        agent = module.create_test_agent("test-model", name="MyTestAgent")
        print(f"✓ Agent创建成功: {agent.name}")
        print(f"✓ 模型配置: {agent.llm_config}")
        
        # 清理
        os.unlink(temp_file)
        print("✓ 清理完成")
        
        return True
        
    except Exception as e:
        print(f"✗ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("开始Agent管理器功能测试...\n")
    
    success = True
    success &= test_agent_manager()
    success &= test_agent_creation()
    
    print(f"\n{'='*50}")
    if success:
        print("✓ 所有测试通过！")
        return 0
    else:
        print("✗ 部分测试失败！")
        return 1

if __name__ == "__main__":
    sys.exit(main())
