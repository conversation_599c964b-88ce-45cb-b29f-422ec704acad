#!/usr/bin/env python3
"""
高级Team功能测试

测试使用TerminationManager的高级Team功能：
1. 场景化终止条件的Team创建
2. 自定义终止条件的Team创建
3. 复杂终止条件组合的验证
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from managers.team_manager import get_team_manager
from managers.agent_manager import get_agent_manager
from managers.termination_manager import get_termination_manager


def test_advanced_team_creation():
    """测试高级Team创建功能"""
    print("=== 高级Team创建测试 ===")
    
    try:
        # 获取管理器实例
        team_manager = get_team_manager()
        agent_manager = get_agent_manager()
        termination_manager = get_termination_manager()
        
        print(f"✓ 管理器实例获取成功")
        
        # 注册高级Team工厂函数
        from teams.advanced_team import (
            create_advanced_development_team,
            create_custom_termination_team,
            create_timeout_sensitive_team,
            create_approval_based_team
        )
        
        team_manager.register_team_factory("advanced_development", create_advanced_development_team)
        team_manager.register_team_factory("custom_termination", create_custom_termination_team)
        team_manager.register_team_factory("timeout_sensitive", create_timeout_sensitive_team)
        team_manager.register_team_factory("approval_based", create_approval_based_team)
        
        print(f"✓ 高级Team工厂函数注册成功")
        
        # 测试场景化终止条件Team
        try:
            scenario_team = team_manager.create_team(
                "advanced_development",
                "nebulacoder-v6.0",
                scenario="development_team"
            )
            print(f"✓ 场景化终止条件Team创建成功: {type(scenario_team)}")
        except Exception as e:
            error_msg = str(e)
            if ("MODEL_CONFIGURATION_ERROR" in error_msg or 
                "proxy" in error_msg.lower() or 
                "No agents available" in error_msg):
                print(f"⚠ 场景化Team创建因模型配置问题跳过: {error_msg}")
            else:
                print(f"✗ 场景化Team创建失败: {e}")
                return False
        
        # 测试自定义终止条件Team
        try:
            custom_config = {
                "conditions": [
                    {"type": "max_message", "max_messages": 8},
                    {"type": "text_mention", "text": "CUSTOM_DONE"}
                ],
                "operator": "or"
            }
            custom_team = team_manager.create_team(
                "custom_termination",
                "nebulacoder-v6.0",
                termination_config=custom_config
            )
            print(f"✓ 自定义终止条件Team创建成功: {type(custom_team)}")
        except Exception as e:
            error_msg = str(e)
            if ("MODEL_CONFIGURATION_ERROR" in error_msg or 
                "proxy" in error_msg.lower() or 
                "No agents available" in error_msg):
                print(f"⚠ 自定义Team创建因模型配置问题跳过: {error_msg}")
            else:
                print(f"✗ 自定义Team创建失败: {e}")
                return False
        
        return True
        
    except Exception as e:
        print(f"✗ 高级Team创建测试失败: {e}")
        return False


def test_termination_scenarios():
    """测试不同终止条件场景"""
    print("\n=== 终止条件场景测试 ===")
    
    try:
        termination_manager = get_termination_manager()
        
        # 测试开发团队场景
        try:
            dev_config = {
                "conditions": [
                    {"type": "max_message", "max_messages": 20},
                    {"type": "text_mention", "text": "LGTM"},
                    {"type": "text_mention", "text": "APPROVED"}
                ],
                "operator": "or"
            }
            dev_condition = termination_manager.create_from_config(dev_config)
            print(f"✓ 开发团队终止条件创建成功: {type(dev_condition)}")
        except Exception as e:
            print(f"✗ 开发团队终止条件创建失败: {e}")
        
        # 测试代码审查场景
        try:
            review_config = {
                "conditions": [
                    {"type": "max_message", "max_messages": 12},
                    {"type": "text_mention", "text": "REVIEW_COMPLETE"},
                    {"type": "timeout", "timeout_seconds": 300.0}
                ],
                "operator": "or"
            }
            review_condition = termination_manager.create_from_config(review_config)
            print(f"✓ 代码审查终止条件创建成功: {type(review_condition)}")
        except Exception as e:
            print(f"✗ 代码审查终止条件创建失败: {e}")
        
        # 测试快速对话场景
        try:
            quick_config = {
                "conditions": [
                    {"type": "max_message", "max_messages": 6},
                    {"type": "timeout", "timeout_seconds": 30.0}
                ],
                "operator": "or"
            }
            quick_condition = termination_manager.create_from_config(quick_config)
            print(f"✓ 快速对话终止条件创建成功: {type(quick_condition)}")
        except Exception as e:
            print(f"✗ 快速对话终止条件创建失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"✗ 终止条件场景测试失败: {e}")
        return False


def test_complex_termination_combinations():
    """测试复杂终止条件组合"""
    print("\n=== 复杂终止条件组合测试 ===")
    
    try:
        termination_manager = get_termination_manager()
        
        # 测试多重OR组合
        try:
            multi_or_config = {
                "conditions": [
                    {"type": "max_message", "max_messages": 15},
                    {"type": "text_mention", "text": "DONE"},
                    {"type": "text_mention", "text": "COMPLETE"},
                    {"type": "text_mention", "text": "FINISHED"},
                    {"type": "timeout", "timeout_seconds": 120.0}
                ],
                "operator": "or"
            }
            multi_or_condition = termination_manager.create_from_config(multi_or_config)
            print(f"✓ 多重OR组合终止条件创建成功: {type(multi_or_condition)}")
        except Exception as e:
            print(f"✗ 多重OR组合终止条件创建失败: {e}")
        
        # 测试AND组合
        try:
            and_config = {
                "conditions": [
                    {"type": "max_message", "max_messages": 50},
                    {"type": "token_usage", "max_prompt_tokens": 5000, "max_completion_tokens": 2000}
                ],
                "operator": "and"
            }
            and_condition = termination_manager.create_from_config(and_config)
            print(f"✓ AND组合终止条件创建成功: {type(and_condition)}")
        except Exception as e:
            print(f"✗ AND组合终止条件创建失败: {e}")
        
        # 测试单个条件
        try:
            single_config = {"type": "max_message", "max_messages": 10}
            single_condition = termination_manager.create_from_config(single_config)
            print(f"✓ 单个终止条件创建成功: {type(single_condition)}")
        except Exception as e:
            print(f"✗ 单个终止条件创建失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"✗ 复杂终止条件组合测试失败: {e}")
        return False


def test_team_manager_integration():
    """测试Team管理器与终止条件管理器的集成"""
    print("\n=== Team管理器集成测试 ===")
    
    try:
        team_manager = get_team_manager()
        
        # 获取可用的teams
        available_teams = team_manager.list_available_teams()
        print(f"✓ 可用Teams: {available_teams}")
        
        # 测试现有team的终止条件功能
        if "development" in available_teams:
            try:
                # 使用自定义终止条件配置
                custom_termination_config = {
                    "conditions": [
                        {"type": "max_message", "max_messages": 5},
                        {"type": "text_mention", "text": "TEST_COMPLETE"}
                    ],
                    "operator": "or"
                }
                
                team = team_manager.create_team(
                    "development",
                    "nebulacoder-v6.0",
                    termination_config=custom_termination_config
                )
                print(f"✓ 带自定义终止条件的Team创建成功: {type(team)}")
            except Exception as e:
                error_msg = str(e)
                if ("MODEL_CONFIGURATION_ERROR" in error_msg or 
                    "proxy" in error_msg.lower() or 
                    "No agents available" in error_msg):
                    print(f"⚠ Team创建因模型配置问题跳过: {error_msg}")
                else:
                    print(f"✗ 带自定义终止条件的Team创建失败: {e}")
                    return False
        
        return True
        
    except Exception as e:
        print(f"✗ Team管理器集成测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("开始高级Team功能测试...")
    
    tests = [
        test_advanced_team_creation,
        test_termination_scenarios,
        test_complex_termination_combinations,
        test_team_manager_integration,
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
                print(f"✓ {test.__name__} 通过")
            else:
                print(f"✗ {test.__name__} 失败")
        except Exception as e:
            print(f"✗ {test.__name__} 异常: {e}")
    
    print(f"\n=== 测试总结 ===")
    print(f"总测试数: {total}")
    print(f"通过数: {passed}")
    print(f"失败数: {total - passed}")
    print(f"成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("🎉 所有测试通过！高级Team功能正常。")
        return True
    else:
        print("❌ 部分测试失败，请检查高级Team实现。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
