#!/usr/bin/env python3
"""
综合演示Agent和Team智能model_alias处理功能

展示create_agent和create_team中model_alias为空时的智能处理机制：
1. 如果工厂函数中已经配置了默认的model_alias，就不传入model_alias参数
2. 如果没有配置默认模型，则使用model_config.yaml中的primary_model作为默认值
3. 并且要给出告警提示
"""

import sys
import os
import inspect
sys.path.append(os.path.dirname(__file__))

def demo_comprehensive_smart_model_alias():
    """综合演示Agent和Team智能model_alias处理功能"""
    print("🎯 AutoGen智能model_alias处理功能综合演示")
    print("=" * 80)
    
    try:
        from managers.agent_manager import get_agent_manager
        from managers.team_manager import get_team_manager
        from managers.model_manager import get_model_manager
        
        # 初始化管理器
        agent_manager = get_agent_manager()
        team_manager = get_team_manager()
        model_manager = get_model_manager()
        
        print(f"📋 系统配置:")
        print(f"  - 主要模型: {model_manager.get_primary_model()}")
        print(f"  - 备用模型: {model_manager.get_fallback_model()}")
        print(f"  - 可用Agent类型: {len(agent_manager.list_available_agents())}个")
        print(f"  - 可用Team类型: {len(team_manager.list_available_teams())}个")
        
        # 分析Agent工厂函数签名
        print(f"\n🔍 Agent工厂函数签名分析:")
        agent_with_defaults = []
        agent_without_defaults = []
        
        for agent_type in agent_manager.list_available_agents():
            factory_func = agent_manager._factories.get(agent_type)
            if factory_func:
                sig = inspect.signature(factory_func)
                model_param = sig.parameters.get('model_alias')
                
                if model_param and model_param.default != inspect.Parameter.empty:
                    agent_with_defaults.append(agent_type)
                else:
                    agent_without_defaults.append(agent_type)
        
        print(f"  ✅ 有默认值的Agent: {agent_with_defaults}")
        print(f"  ⚠️ 需要model的Agent: {agent_without_defaults}")
        
        # 分析Team工厂函数签名
        print(f"\n🔍 Team工厂函数签名分析:")
        team_with_defaults = []
        team_without_defaults = []
        
        for team_type in team_manager.list_available_teams():
            factory_func = team_manager._factories.get(team_type)
            if factory_func:
                sig = inspect.signature(factory_func)
                model_param = sig.parameters.get('model_alias')
                
                if model_param and model_param.default != inspect.Parameter.empty:
                    team_with_defaults.append(team_type)
                else:
                    team_without_defaults.append(team_type)
        
        print(f"  ✅ 有默认值的Team: {team_with_defaults}")
        print(f"  ⚠️ 需要model的Team: {team_without_defaults}")
        
        # 演示Agent智能处理
        print(f"\n🧪 Agent智能model_alias处理演示:")
        
        # 演示有默认值的agent
        if agent_with_defaults:
            print(f"\n📝 场景1: 创建有默认值的Agent ({agent_with_defaults[0]})")
            try:
                agent = agent_manager.create_agent(agent_with_defaults[0], model_alias=None, name='DemoAgent1')
                print(f"  ✅ 创建成功: {type(agent).__name__}")
                print(f"  💡 说明: 有默认值，不传入model_alias，不会有告警")
            except Exception as e:
                print(f"  ⚠️ 创建失败（预期）: {str(e)[:80]}...")
        
        # 演示需要model的agent
        if agent_without_defaults:
            print(f"\n📝 场景2: 创建需要model的Agent ({agent_without_defaults[0]})")
            try:
                agent = agent_manager.create_agent(agent_without_defaults[0], model_alias=None, name='DemoAgent2')
                print(f"  ✅ 创建成功: {type(agent).__name__}")
                print(f"  💡 说明: 无默认值，使用primary_model，会有告警")
            except Exception as e:
                print(f"  ⚠️ 创建失败（预期）: {str(e)[:80]}...")
        
        # 演示Team智能处理
        print(f"\n🧪 Team智能model_alias处理演示:")
        
        # 演示有默认值的team
        if team_with_defaults:
            print(f"\n📝 场景3: 创建有默认值的Team ({team_with_defaults[0]})")
            try:
                team = team_manager.create_team(team_with_defaults[0], model_alias=None)
                print(f"  ✅ 创建成功: {type(team).__name__}")
                print(f"  💡 说明: 有默认值，不传入model_alias，不会有告警")
            except Exception as e:
                print(f"  ⚠️ 创建失败（预期）: {str(e)[:80]}...")
        
        # 演示需要model的team
        if team_without_defaults:
            print(f"\n📝 场景4: 创建需要model的Team ({team_without_defaults[0]})")
            try:
                team = team_manager.create_team(team_without_defaults[0], model_alias=None)
                print(f"  ✅ 创建成功: {type(team).__name__}")
                print(f"  💡 说明: 无默认值，使用primary_model，会有告警")
            except Exception as e:
                print(f"  ⚠️ 创建失败（预期）: {str(e)[:80]}...")
        
        # 演示提供participants的team
        if team_with_defaults:
            print(f"\n📝 场景5: 提供participants的Team")
            try:
                # 创建agents作为participants
                agent1 = agent_manager.create_agent('user_proxy', name='Participant1')
                agent2 = agent_manager.create_agent('user_proxy', name='Participant2')
                participants = [agent1, agent2]
                
                team = team_manager.create_team(
                    team_with_defaults[0], 
                    model_alias=None, 
                    participants=participants
                )
                print(f"  ✅ 创建成功: {type(team).__name__}")
                print(f"  💡 说明: 提供了participants，不需要创建默认agents，不需要model_alias")
            except Exception as e:
                print(f"  ⚠️ 创建失败（预期）: {str(e)[:80]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ 演示失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def demo_api_best_practices():
    """演示API最佳实践"""
    print(f"\n🚀 API使用最佳实践")
    print("=" * 80)
    
    print(f"📖 推荐的使用方式:")
    
    print(f"\n1️⃣ Agent API:")
    print(f"   # 不需要model的agent")
    print(f"   POST /autogen/run/agent")
    print(f"   {{\"name\": \"user_proxy\", \"message\": \"用户消息\"}}")
    print(f"   ")
    print(f"   # 需要model的agent - 推荐明确指定")
    print(f"   POST /autogen/run/agent")
    print(f"   {{\"name\": \"code_reviewer\", \"message\": \"请审查代码\", \"model\": \"gpt-4o-mini\"}}")
    print(f"   ")
    print(f"   # 需要model的agent - 自动使用primary_model（会有告警）")
    print(f"   POST /autogen/run/agent")
    print(f"   {{\"name\": \"code_reviewer\", \"message\": \"请审查代码\"}}")
    
    print(f"\n2️⃣ Team API:")
    print(f"   # 不需要model的team（有默认值或提供participants）")
    print(f"   POST /autogen/run/team")
    print(f"   {{\"name\": \"round_robin_group_chat\", \"message\": \"团队协作消息\"}}")
    print(f"   ")
    print(f"   # 需要model的team - 推荐明确指定")
    print(f"   POST /autogen/run/team")
    print(f"   {{\"name\": \"development\", \"message\": \"开发任务\", \"model\": \"gpt-4o-mini\"}}")
    print(f"   ")
    print(f"   # 需要model的team - 自动使用primary_model（会有告警）")
    print(f"   POST /autogen/run/team")
    print(f"   {{\"name\": \"development\", \"message\": \"开发任务\"}}")
    
    print(f"\n3️⃣ 编程API:")
    print(f"   # Agent - 智能处理")
    print(f"   agent = agent_manager.create_agent('user_proxy')  # 不需要model")
    print(f"   agent = agent_manager.create_agent('code_reviewer', 'gpt-4o-mini')  # 明确指定")
    print(f"   agent = agent_manager.create_agent('code_reviewer')  # 自动使用primary_model")
    print(f"   ")
    print(f"   # Team - 智能处理")
    print(f"   team = team_manager.create_team('round_robin_group_chat')  # 不需要model")
    print(f"   team = team_manager.create_team('development', 'gpt-4o-mini')  # 明确指定")
    print(f"   team = team_manager.create_team('development')  # 自动使用primary_model")
    
    print(f"\n💡 最佳实践总结:")
    print(f"  - 🎯 明确指定model参数是最佳实践")
    print(f"  - 🧠 系统会智能识别是否需要model参数")
    print(f"  - ⚡ 自动使用合适的默认值")
    print(f"  - 🔔 适时提供告警提示")
    print(f"  - 🔄 保持向后兼容性")
    print(f"  - 📊 支持多种使用场景")


def main():
    """主函数"""
    print("🎯 AutoGen智能model_alias处理功能综合演示")
    print("=" * 100)
    
    # 运行演示
    success = demo_comprehensive_smart_model_alias()
    
    if success:
        demo_api_best_practices()
        
        print(f"\n🎉 综合演示完成！")
        print(f"智能model_alias处理功能已在Agent和Team中成功实现：")
        print(f"")
        print(f"🔧 核心特性:")
        print(f"  - 🧠 智能识别工厂函数的model需求")
        print(f"  - ⚡ 自动使用合适的默认值")
        print(f"  - 🔔 适时提供告警提示")
        print(f"  - 🔄 保持向后兼容性")
        print(f"  - 📊 支持多种使用场景")
        print(f"")
        print(f"✅ Agent支持:")
        print(f"  - user_proxy, code_executor: 有默认值，不需要model")
        print(f"  - code_reviewer, assistant等: 无默认值，自动使用primary_model")
        print(f"")
        print(f"✅ Team支持:")
        print(f"  - round_robin_group_chat, swarm: 有默认值，不需要model")
        print(f"  - development, advanced_development等: 无默认值，自动使用primary_model")
        print(f"  - 提供participants时: 可以不需要model参数")
        print(f"")
        print(f"🚀 现在可以继续进行Phase 3.3的API测试了！")
        
        return True
    else:
        print(f"\n❌ 演示失败，请检查实现。")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
